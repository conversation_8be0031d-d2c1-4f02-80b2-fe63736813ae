import java.awt.*;
import java.awt.event.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import javax.swing.*;
import javax.swing.border.*;
import javax.swing.filechooser.FileNameExtensionFilter;

public class TodoListGUI extends JFrame {
    private List<String> tasks;
    private DefaultListModel<TaskPanel> listModel;
    private JList<TaskPanel> taskList;
    private JTextField taskInput;
    private JLabel statusLabel;
    private JLabel taskCountLabel;

    private final Color PRIMARY_COLOR = new Color(41, 128, 185);
    private final Color SECONDARY_COLOR = new Color(236, 240, 241);
    private final Color ACCENT_COLOR = new Color(220, 53, 69);        // Brighter red
    private final Color HIGHLIGHT_COLOR = new Color(52, 152, 219);
    private final Color SUCCESS_COLOR = new Color(40, 167, 69);       // Brighter green
    private final Color WARNING_COLOR = new Color(255, 193, 7);       // Brighter yellow/orange
    private final Color INFO_COLOR = new Color(23, 162, 184);         // Cyan blue
    private final Color PURPLE_COLOR = new Color(111, 66, 193);       // Purple
    private final Color TEXT_COLOR = new Color(0, 0, 0);           // Darker text for better contrast
    private final Color LABEL_COLOR = new Color(52, 58, 64);          // Slightly lighter for labels
    private final Color STATUS_COLOR = new Color(108, 117, 125);      // Medium gray for status text

    public TodoListGUI() {
        tasks = new ArrayList<>();

        setTitle("✓ Modern Todo List");
        setSize(600, 700);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setIconImage(createAppIcon());

        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Create menu bar
        createMenuBar();

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10)) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, SECONDARY_COLOR, 0, h, new Color(224, 234, 252));
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        JPanel headerPanel = new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, PRIMARY_COLOR, w, 0, HIGHLIGHT_COLOR);
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };

        JLabel titleLabel = new JLabel("✓ My Todo List", JLabel.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 32));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(18, 0, 18, 0));
        // Add text shadow effect
        titleLabel.setText("<html><div style='text-shadow: 2px 2px 4px rgba(0,0,0,0.5);'>✓ My Todo List</div></html>");
        headerPanel.add(titleLabel, BorderLayout.CENTER);

        JPanel inputPanel = new JPanel(new BorderLayout(10, 0));
        inputPanel.setOpaque(false);
        inputPanel.setBorder(BorderFactory.createEmptyBorder(20, 0, 20, 0));

        JLabel inputLabel = new JLabel("📝 Enter New Task:");
        inputLabel.setFont(new Font("Segoe UI", Font.BOLD, 16));
        inputLabel.setForeground(TEXT_COLOR);

        taskInput = new JTextField();
        taskInput.setFont(new Font("Segoe UI", Font.PLAIN, 18));
        taskInput.setForeground(TEXT_COLOR);
        taskInput.setBackground(Color.WHITE);
        taskInput.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(180, 180, 180), 2),
                BorderFactory.createEmptyBorder(12, 15, 12, 15)
        ));
        // Add focus border effect
        taskInput.addFocusListener(new FocusAdapter() {
            @Override
            public void focusGained(FocusEvent e) {
                taskInput.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(PRIMARY_COLOR, 3),
                        BorderFactory.createEmptyBorder(11, 14, 11, 14)
                ));
            }

            @Override
            public void focusLost(FocusEvent e) {
                taskInput.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(180, 180, 180), 2),
                        BorderFactory.createEmptyBorder(12, 15, 12, 15)
                ));
            }
        });

        JButton addButton = createStyledButton("➕ Add Task", PRIMARY_COLOR, Color.WHITE);

        inputPanel.add(inputLabel, BorderLayout.NORTH);
        inputPanel.add(taskInput, BorderLayout.CENTER);
        inputPanel.add(addButton, BorderLayout.EAST);

        listModel = new DefaultListModel<>();
        taskList = new JList<>(listModel);
        taskList.setCellRenderer((list, value, index, isSelected, cellHasFocus) -> value);
        taskList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        taskList.setFixedCellHeight(60);
        taskList.setOpaque(false);
        JScrollPane scrollPane = new JScrollPane(taskList);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(200, 200, 200), 1),
                BorderFactory.createEmptyBorder(1, 1, 1, 1)
        ));

        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setOpaque(false);
        statusPanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(200, 200, 200)),
                BorderFactory.createEmptyBorder(10, 0, 0, 0)
        ));

        statusLabel = new JLabel("🚀 Ready to add tasks");
        statusLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        statusLabel.setForeground(STATUS_COLOR);

        taskCountLabel = new JLabel("📊 Tasks: 0");
        taskCountLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
        taskCountLabel.setForeground(PRIMARY_COLOR);

        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(taskCountLabel, BorderLayout.EAST);

        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        buttonPanel.setOpaque(false);
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(20, 0, 10, 0));

        JButton saveButton = createStyledButton("💾 Save", SUCCESS_COLOR, Color.WHITE);
        JButton loadButton = createStyledButton("📁 Load", INFO_COLOR, Color.WHITE);
        JButton clearButton = createStyledButton("🗑️ Clear All", ACCENT_COLOR, Color.WHITE);

        buttonPanel.add(saveButton);
        buttonPanel.add(loadButton);
        buttonPanel.add(clearButton);

        addButton.addActionListener(e -> addTask());
        taskInput.addActionListener(e -> addTask());
        saveButton.addActionListener(e -> saveToFile());
        loadButton.addActionListener(e -> loadFromFile());
        clearButton.addActionListener(e -> clearTasks());

        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(inputPanel, BorderLayout.PAGE_START);
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);
        mainPanel.add(statusPanel, BorderLayout.PAGE_END);

        add(mainPanel);
        updateTaskCount(); // Initialize task count
        setVisible(true);
    }

    private JButton createStyledButton(String text, Color bgColor, Color fgColor) {
        JButton button = new JButton(text);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setFocusPainted(false);
        button.setOpaque(true);
        button.setBorderPainted(true);
        button.setFont(new Font("Segoe UI", Font.BOLD, 16));

        // Enhanced border with shadow effect
        button.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createRaisedBevelBorder(),
                BorderFactory.createCompoundBorder(
                    new LineBorder(bgColor.darker(), 2, true),
                    BorderFactory.createEmptyBorder(12, 24, 12, 24)
                )
        ));

        // Add drop shadow effect
        button.setContentAreaFilled(true);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(bgColor.brighter());
                button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createRaisedBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(bgColor.darker(), 3, true),
                            BorderFactory.createEmptyBorder(11, 23, 11, 23)
                        )
                ));
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
                button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createRaisedBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(bgColor.darker(), 2, true),
                            BorderFactory.createEmptyBorder(12, 24, 12, 24)
                        )
                ));
            }

            @Override
            public void mousePressed(MouseEvent e) {
                button.setBackground(bgColor.darker());
                button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLoweredBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(bgColor.darker().darker(), 2, true),
                            BorderFactory.createEmptyBorder(12, 24, 12, 24)
                        )
                ));
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                button.setBackground(bgColor.brighter());
                button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createRaisedBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(bgColor.darker(), 3, true),
                            BorderFactory.createEmptyBorder(11, 23, 11, 23)
                        )
                ));
            }
        });

        return button;
    }

    private void addTask() {
        String taskText = taskInput.getText().trim();
        if (!taskText.isEmpty()) {
            tasks.add(taskText);
            TaskPanel taskPanel = new TaskPanel(taskText);
            listModel.addElement(taskPanel);
            taskInput.setText("");
            statusLabel.setText("✅ Task added: " + taskText);
            statusLabel.setForeground(SUCCESS_COLOR);
            resetStatusColorAfterDelay();
            updateTaskCount();
            taskInput.requestFocus();
        } else {
            statusLabel.setText("⚠️ Please enter a task description");
            statusLabel.setForeground(WARNING_COLOR);
            // Reset color after 3 seconds
            Timer timer = new Timer(3000, e -> statusLabel.setForeground(new Color(100, 100, 100)));
            timer.setRepeats(false);
            timer.start();
        }
    }

    private void clearTasks() {
        if (!tasks.isEmpty()) {
            int confirm = JOptionPane.showConfirmDialog(
                    this,
                    "Are you sure you want to clear all " + tasks.size() + " tasks?",
                    "Confirm Clear All",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE
            );

            if (confirm == JOptionPane.YES_OPTION) {
                listModel.clear();
                tasks.clear();
                statusLabel.setText("🗑️ All tasks cleared");
                statusLabel.setForeground(ACCENT_COLOR);
                updateTaskCount();
            }
        } else {
            statusLabel.setText("ℹ️ No tasks to clear");
            statusLabel.setForeground(INFO_COLOR);
        }
    }

    private void createMenuBar() {
        JMenuBar menuBar = new JMenuBar();
        menuBar.setBackground(SECONDARY_COLOR);

        JMenu fileMenu = new JMenu("📁 File");
        fileMenu.setFont(new Font("Segoe UI", Font.BOLD, 15));
        fileMenu.setForeground(TEXT_COLOR);

        JMenuItem saveItem = new JMenuItem("💾 Save Tasks");
        JMenuItem loadItem = new JMenuItem("📂 Load Tasks");
        JMenuItem exitItem = new JMenuItem("🚪 Exit");

        // Enhance menu item fonts
        saveItem.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        loadItem.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        exitItem.setFont(new Font("Segoe UI", Font.PLAIN, 14));

        saveItem.addActionListener(e -> saveToFile());
        loadItem.addActionListener(e -> loadFromFile());
        exitItem.addActionListener(e -> System.exit(0));

        fileMenu.add(saveItem);
        fileMenu.add(loadItem);
        fileMenu.addSeparator();
        fileMenu.add(exitItem);

        menuBar.add(fileMenu);
        setJMenuBar(menuBar);
    }

    private Image createAppIcon() {
        // Create a simple icon programmatically
        int size = 32;
        BufferedImage icon = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = icon.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(PRIMARY_COLOR);
        g2d.fillRoundRect(2, 2, size-4, size-4, 8, 8);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Arial", Font.BOLD, 20));
        g2d.drawString("✓", 8, 22);
        g2d.dispose();
        return icon;
    }

    private void updateTaskCount() {
        taskCountLabel.setText("📊 Tasks: " + tasks.size());
        taskCountLabel.setForeground(tasks.size() > 0 ? SUCCESS_COLOR : PRIMARY_COLOR);
    }

    private void resetStatusColorAfterDelay() {
        Timer timer = new Timer(4000, e -> statusLabel.setForeground(STATUS_COLOR));
        timer.setRepeats(false);
        timer.start();
    }

    private void saveToFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new FileNameExtensionFilter("Text Files", "txt"));
        fileChooser.setSelectedFile(new File("my_tasks.txt"));

        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
                for (String task : tasks) {
                    writer.write(task);
                    writer.newLine();
                }
                statusLabel.setText("💾 Tasks saved to " + file.getName());
                statusLabel.setForeground(SUCCESS_COLOR);
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, "Error saving file: " + e.getMessage(),
                    "Save Error", JOptionPane.ERROR_MESSAGE);
                statusLabel.setText("❌ Error saving tasks");
                statusLabel.setForeground(ACCENT_COLOR);
            }
        }
    }

    private void loadFromFile() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileFilter(new FileNameExtensionFilter("Text Files", "txt"));

        if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            File file = fileChooser.getSelectedFile();
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                listModel.clear();
                tasks.clear();

                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        tasks.add(line);
                        TaskPanel taskPanel = new TaskPanel(line);
                        listModel.addElement(taskPanel);
                    }
                }
                statusLabel.setText("📁 Tasks loaded from " + file.getName());
                statusLabel.setForeground(INFO_COLOR);
                updateTaskCount();
            } catch (IOException e) {
                JOptionPane.showMessageDialog(this, "Error loading file: " + e.getMessage(),
                    "Load Error", JOptionPane.ERROR_MESSAGE);
                statusLabel.setText("❌ Error loading tasks");
                statusLabel.setForeground(ACCENT_COLOR);
            }
        }
    }

    private class TaskPanel extends JPanel {
        private final String task;
        private boolean isCompleted = false;

        public TaskPanel(String task) {
            this.task = task;
            setLayout(new BorderLayout(10, 0));
            setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(220, 220, 220), 1),
                BorderFactory.createEmptyBorder(12, 15, 12, 15)
            ));
            setBackground(new Color(255, 255, 255, 240));

            // Task content panel
            JPanel contentPanel = new JPanel(new BorderLayout(10, 0));
            contentPanel.setOpaque(false);

            // Checkbox for completion
            JCheckBox completeBox = new JCheckBox();
            completeBox.setOpaque(false);
            completeBox.setFocusPainted(false);
            completeBox.addActionListener(e -> toggleCompletion());

            JLabel taskLabel = new JLabel("📝 " + task);
            taskLabel.setFont(new Font("Segoe UI", Font.PLAIN, 18));
            taskLabel.setForeground(TEXT_COLOR);

            // Button panel
            JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
            buttonPanel.setOpaque(false);

            JButton editBtn = createSmallButton("✏️ Edit", PURPLE_COLOR);
            JButton deleteBtn = createSmallButton("🗑️ Delete", ACCENT_COLOR);

            editBtn.addActionListener(e -> editTask());
            deleteBtn.addActionListener(e -> deleteTask());

            buttonPanel.add(editBtn);
            buttonPanel.add(deleteBtn);

            contentPanel.add(completeBox, BorderLayout.WEST);
            contentPanel.add(taskLabel, BorderLayout.CENTER);

            add(contentPanel, BorderLayout.CENTER);
            add(buttonPanel, BorderLayout.EAST);

            // Add hover effect
            addMouseListener(new MouseAdapter() {
                @Override
                public void mouseEntered(MouseEvent e) {
                    setBackground(new Color(245, 248, 255, 240));
                    setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(HIGHLIGHT_COLOR, 2),
                        BorderFactory.createEmptyBorder(11, 14, 11, 14)
                    ));
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    setBackground(new Color(255, 255, 255, 240));
                    setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(220, 220, 220), 1),
                        BorderFactory.createEmptyBorder(12, 15, 12, 15)
                    ));
                }
            });
        }

        private JButton createSmallButton(String text, Color color) {
            JButton button = new JButton(text);
            button.setBackground(color);
            button.setForeground(Color.WHITE);
            button.setFocusPainted(false);
            button.setOpaque(true);
            button.setBorderPainted(true);
            button.setFont(new Font("Segoe UI", Font.BOLD, 13));
            button.setCursor(new Cursor(Cursor.HAND_CURSOR));

            // Enhanced border for better visibility
            button.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createRaisedBevelBorder(),
                BorderFactory.createCompoundBorder(
                    new LineBorder(color.darker(), 1, true),
                    BorderFactory.createEmptyBorder(6, 12, 6, 12)
                )
            ));

            button.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseEntered(MouseEvent e) {
                    button.setBackground(color.brighter());
                    button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createRaisedBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(color.darker(), 2, true),
                            BorderFactory.createEmptyBorder(5, 11, 5, 11)
                        )
                    ));
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    button.setBackground(color);
                    button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createRaisedBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(color.darker(), 1, true),
                            BorderFactory.createEmptyBorder(6, 12, 6, 12)
                        )
                    ));
                }

                @Override
                public void mousePressed(MouseEvent e) {
                    button.setBackground(color.darker());
                    button.setBorder(BorderFactory.createCompoundBorder(
                        BorderFactory.createLoweredBevelBorder(),
                        BorderFactory.createCompoundBorder(
                            new LineBorder(color.darker().darker(), 1, true),
                            BorderFactory.createEmptyBorder(6, 12, 6, 12)
                        )
                    ));
                }

                @Override
                public void mouseReleased(MouseEvent e) {
                    button.setBackground(color.brighter());
                }
            });

            return button;
        }

        private void toggleCompletion() {
            isCompleted = !isCompleted;
            Component[] components = getComponents();
            for (Component comp : components) {
                if (comp instanceof JPanel) {
                    JPanel panel = (JPanel) comp;
                    for (Component subComp : panel.getComponents()) {
                        if (subComp instanceof JLabel) {
                            JLabel label = (JLabel) subComp;
                            if (isCompleted) {
                                label.setText("<html><strike>📝 " + task + "</strike></html>");
                                label.setForeground(new Color(150, 150, 150));
                                setBackground(new Color(240, 240, 240, 200));
                            } else {
                                label.setText("📝 " + task);
                                label.setForeground(TEXT_COLOR);
                                setBackground(new Color(255, 255, 255, 240));
                            }
                            break;
                        }
                    }
                    break;
                }
            }
        }

        private void editTask() {
            String newTask = JOptionPane.showInputDialog(this, "Edit task:", task);
            if (newTask != null && !newTask.trim().isEmpty()) {
                int index = listModel.indexOf(this);
                if (index != -1) {
                    tasks.set(index, newTask.trim());
                    // Update the label
                    Component[] components = getComponents();
                    for (Component comp : components) {
                        if (comp instanceof JPanel) {
                            JPanel panel = (JPanel) comp;
                            for (Component subComp : panel.getComponents()) {
                                if (subComp instanceof JLabel) {
                                    JLabel label = (JLabel) subComp;
                                    label.setText("📝 " + newTask.trim());
                                    break;
                                }
                            }
                            break;
                        }
                    }
                    statusLabel.setText("✏️ Task updated");
                    statusLabel.setForeground(PURPLE_COLOR);
                }
            }
        }

        private void deleteTask() {
            int confirm = JOptionPane.showConfirmDialog(
                this,
                "Delete this task?",
                "Confirm Delete",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.QUESTION_MESSAGE
            );

            if (confirm == JOptionPane.YES_OPTION) {
                int index = listModel.indexOf(this);
                if (index != -1) {
                    listModel.remove(index);
                    tasks.remove(index);
                    statusLabel.setText("🗑️ Task deleted: " + task);
                    statusLabel.setForeground(ACCENT_COLOR);
                    updateTaskCount();
                }
            }
        }

        @Override
        public Dimension getPreferredSize() {
            return new Dimension(super.getPreferredSize().width, 65);
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(TodoListGUI::new);
    }
}
