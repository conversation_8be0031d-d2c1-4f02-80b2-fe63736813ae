import java.awt.*;
import java.awt.event.*;
import java.util.ArrayList;
import java.util.List;
import javax.swing.*;
import javax.swing.border.*;

public class TodoListGUI extends JFrame {
    private List<String> tasks;
    private DefaultListModel<TaskPanel> listModel;
    private JList<TaskPanel> taskList;
    private JTextField taskInput;
    private JLabel statusLabel;

    private final Color PRIMARY_COLOR = new Color(41, 128, 185);
    private final Color SECONDARY_COLOR = new Color(236, 240, 241);
    private final Color ACCENT_COLOR = new Color(231, 76, 60);
    private final Color HIGHLIGHT_COLOR = new Color(52, 152, 219);
    private final Color TEXT_COLOR = new Color(44, 62, 80);

    public TodoListGUI() {
        tasks = new ArrayList<>();

        setTitle("Todo List Application");
        setSize(550, 650);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLocationRelativeTo(null);

        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            SwingUtilities.updateComponentTreeUI(this);
        } catch (Exception e) {
            e.printStackTrace();
        }

        JPanel mainPanel = new JPanel(new BorderLayout(10, 10)) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, SECONDARY_COLOR, 0, h, new Color(224, 234, 252));
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };
        mainPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));

        JPanel headerPanel = new JPanel(new BorderLayout()) {
            @Override
            protected void paintComponent(Graphics g) {
                super.paintComponent(g);
                Graphics2D g2d = (Graphics2D) g;
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                int w = getWidth(), h = getHeight();
                GradientPaint gp = new GradientPaint(0, 0, PRIMARY_COLOR, w, 0, HIGHLIGHT_COLOR);
                g2d.setPaint(gp);
                g2d.fillRect(0, 0, w, h);
            }
        };

        JLabel titleLabel = new JLabel("My Todo List", JLabel.CENTER);
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 28));
        titleLabel.setForeground(Color.WHITE);
        titleLabel.setBorder(BorderFactory.createEmptyBorder(15, 0, 15, 0));
        headerPanel.add(titleLabel, BorderLayout.CENTER);

        JPanel inputPanel = new JPanel(new BorderLayout(10, 0));
        inputPanel.setOpaque(false);
        inputPanel.setBorder(BorderFactory.createEmptyBorder(20, 0, 20, 0));

        JLabel inputLabel = new JLabel("New Task:");
        inputLabel.setFont(new Font("Segoe UI", Font.BOLD, 14));
        inputLabel.setForeground(TEXT_COLOR);

        taskInput = new JTextField();
        taskInput.setFont(new Font("Segoe UI", Font.PLAIN, 16));
        taskInput.setBorder(BorderFactory.createCompoundBorder(
                new SoftBevelBorder(SoftBevelBorder.LOWERED),
                BorderFactory.createEmptyBorder(10, 10, 10, 10)
        ));

        JButton addButton = createStyledButton("Add Task", PRIMARY_COLOR, Color.WHITE);

        inputPanel.add(inputLabel, BorderLayout.NORTH);
        inputPanel.add(taskInput, BorderLayout.CENTER);
        inputPanel.add(addButton, BorderLayout.EAST);

        listModel = new DefaultListModel<>();
        taskList = new JList<>(listModel);
        taskList.setCellRenderer((list, value, index, isSelected, cellHasFocus) -> value);
        taskList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        taskList.setFixedCellHeight(60);
        taskList.setOpaque(false);
        JScrollPane scrollPane = new JScrollPane(taskList);
        scrollPane.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new Color(200, 200, 200), 1),
                BorderFactory.createEmptyBorder(1, 1, 1, 1)
        ));

        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setOpaque(false);
        statusPanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createMatteBorder(1, 0, 0, 0, new Color(200, 200, 200)),
                BorderFactory.createEmptyBorder(10, 0, 0, 0)
        ));

        statusLabel = new JLabel("Ready to add tasks");
        statusLabel.setFont(new Font("Segoe UI", Font.ITALIC, 12));
        statusLabel.setForeground(new Color(100, 100, 100));
        statusPanel.add(statusLabel, BorderLayout.WEST);

        JPanel clearPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 0));
        clearPanel.setOpaque(false);
        clearPanel.setBorder(BorderFactory.createEmptyBorder(20, 0, 10, 0));

        JButton clearButton = createStyledButton("Clear All", ACCENT_COLOR, Color.WHITE);
        clearPanel.add(clearButton);

        addButton.addActionListener(e -> addTask());
        taskInput.addActionListener(e -> addTask());
        clearButton.addActionListener(e -> clearTasks());

        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(inputPanel, BorderLayout.PAGE_START);
        mainPanel.add(scrollPane, BorderLayout.CENTER);
        mainPanel.add(clearPanel, BorderLayout.SOUTH);
        mainPanel.add(statusPanel, BorderLayout.PAGE_END);

        add(mainPanel);
        setVisible(true);
    }

    private JButton createStyledButton(String text, Color bgColor, Color fgColor) {
        JButton button = new JButton(text);
        button.setBackground(bgColor);
        button.setForeground(fgColor);
        button.setFocusPainted(false);
        button.setFont(new Font("Segoe UI", Font.BOLD, 14));
        button.setBorder(BorderFactory.createCompoundBorder(
                new LineBorder(bgColor.darker(), 1, true),
                BorderFactory.createEmptyBorder(10, 20, 10, 20)
        ));

        button.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                button.setBackground(bgColor.darker());
                button.setCursor(new Cursor(Cursor.HAND_CURSOR));
            }

            @Override
            public void mouseExited(MouseEvent e) {
                button.setBackground(bgColor);
                button.setCursor(new Cursor(Cursor.DEFAULT_CURSOR));
            }

            @Override
            public void mousePressed(MouseEvent e) {
                button.setBackground(bgColor.darker().darker());
            }

            @Override
            public void mouseReleased(MouseEvent e) {
                button.setBackground(bgColor.darker());
            }
        });

        return button;
    }

    private void addTask() {
        String taskText = taskInput.getText().trim();
        if (!taskText.isEmpty()) {
            tasks.add(taskText);
            TaskPanel taskPanel = new TaskPanel(taskText);
            listModel.addElement(taskPanel);
            taskInput.setText("");
            statusLabel.setText("Task added: " + taskText);
        }
    }

    private void clearTasks() {
        if (!tasks.isEmpty()) {
            int confirm = JOptionPane.showConfirmDialog(
                    this,
                    "Are you sure you want to clear all tasks?",
                    "Confirm Clear All",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE
            );

            if (confirm == JOptionPane.YES_OPTION) {
                listModel.clear();
                tasks.clear();
                statusLabel.setText("All tasks cleared");
            }
        }
    }

    private class TaskPanel extends JPanel {
        private String task;

        public TaskPanel(String task) {
            this.task = task;
            setLayout(new BorderLayout(10, 0));
            setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));
            setBackground(new Color(255, 255, 255, 230));

            JLabel taskLabel = new JLabel("• " + task);
            taskLabel.setFont(new Font("Segoe UI", Font.PLAIN, 16));
            taskLabel.setForeground(TEXT_COLOR);

            JButton deleteBtn = new JButton("Delete");
            deleteBtn.setFocusPainted(false);
            deleteBtn.setFont(new Font("Segoe UI", Font.PLAIN, 12));
            deleteBtn.setBackground(ACCENT_COLOR);
            deleteBtn.setForeground(Color.WHITE);
            deleteBtn.setBorder(BorderFactory.createEmptyBorder(5, 15, 5, 15));

            deleteBtn.addActionListener(e -> {
                int index = listModel.indexOf(this);
                if (index != -1) {
                    listModel.remove(index);
                    tasks.remove(index);
                    statusLabel.setText("Task deleted: " + task);
                }
            });

            add(taskLabel, BorderLayout.CENTER);
            add(deleteBtn, BorderLayout.EAST);
        }

        @Override
        public Dimension getPreferredSize() {
            return new Dimension(super.getPreferredSize().width, 50);
        }
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(TodoListGUI::new);
    }
}
