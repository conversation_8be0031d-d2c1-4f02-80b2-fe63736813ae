import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

public class TodoList {
    private List<String> tasks;
    
    public TodoList() {
        this.tasks = new ArrayList<>();
    }
    
    public void addTask(String task) {
        tasks.add(task);
        System.out.println("Task added: " + task);
    }
    
    public void displayTasks() {
        if (tasks.isEmpty()) {
            System.out.println("No tasks in your to-do list.");
            return;
        }
        
        System.out.println("Your To-Do List:");
        for (int i = 0; i < tasks.size(); i++) {
            System.out.println((i + 1) + ". " + tasks.get(i));
        }
    }
    
    public void removeTask(int index) {
        if (index < 1 || index > tasks.size()) {
            System.out.println("Invalid task number.");
            return;
        }
        
        String removedTask = tasks.remove(index - 1);
        System.out.println("Task removed: " + removedTask);
    }
    
    public static void main(String[] args) {
        TodoList todoList = new TodoList();
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.println("\n1. Add task\n2. Display tasks\n3. Remove task\n4. Exit");
            System.out.print("Choose an option: ");
            
            int choice = scanner.nextInt();
            scanner.nextLine(); // Consume newline
            
            switch (choice) {
                case 1:
                    System.out.print("Enter task: ");
                    String task = scanner.nextLine();
                    todoList.addTask(task);
                    break;
                case 2:
                    todoList.displayTasks();
                    break;
                case 3:
                    System.out.print("Enter task number to remove: ");
                    int taskNum = scanner.nextInt();
                    todoList.removeTask(taskNum);
                    break;
                case 4:
                    System.out.println("Exiting program. Goodbye!");
                    scanner.close();
                    return;
                default:
                    System.out.println("Invalid option. Try again.");
            }
        }
    }
}